# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=giki_library

# Microsoft OAuth Configuration
MICROSOFT_CLIENT_ID=your_microsoft_client_id
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret
MICROSOFT_CALLBACK_URL=http://localhost:3000/auth/microsoft/callback

# Session Configuration
SESSION_SECRET=giki-library-super-secret-key-change-in-production

# Server Configuration
PORT=3000

# GIKI Email Domains (for validation)
ALLOWED_EMAIL_DOMAINS=@giki.edu.pk,@student.giki.edu.pk
