/* Root Variables */
:root {
    --primary-gold: #d4af37;
    --secondary-bronze: #cd7f32;
    --deep-burgundy: #800020;
    --rich-brown: #5d4037;
    --cream: #f5f5dc;
    --parchment: #f4f2e8;
    --dark-text: #2c1810;
    --light-text: #8b7355;
    
    --vt323: 'VT323', monospace;
    --baskerville: 'Libre Baskerville', serif;
    --garamond: 'Cormorant Garamond', serif;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--baskerville);
    line-height: 1.6;
    color: var(--dark-text);
    background: linear-gradient(135deg, var(--parchment) 0%, var(--cream) 100%);
    overflow-x: hidden;
}

/* Particles Background */
#particles-js {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
    opacity: 0.3;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Landing Header */
.landing-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(244, 242, 232, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--primary-gold);
    transition: all 0.3s ease;
}

.landing-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo-icon {
    font-size: 2rem;
    animation: bookFloat 3s ease-in-out infinite;
}

.logo-text {
    font-family: var(--vt323);
    font-size: 1.8rem;
    color: var(--deep-burgundy);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links a {
    color: var(--dark-text);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--deep-burgundy);
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background: var(--primary-gold);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

.cta-btn {
    background: linear-gradient(45deg, var(--primary-gold), var(--secondary-bronze));
    color: white;
    border: none;
    padding: 0.7rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-top: 100px;
    position: relative;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.hero-left {
    animation: slideInLeft 1s ease-out;
}

.hero-title {
    font-family: var(--vt323);
    font-size: clamp(3rem, 8vw, 6rem);
    line-height: 0.9;
    margin-bottom: 2rem;
    color: var(--dark-text);
}

.title-line {
    display: block;
    opacity: 0;
    animation: fadeInUp 1s ease-out forwards;
}

.title-line:nth-child(1) {
    animation-delay: 0.2s;
}

.title-line:nth-child(2) {
    animation-delay: 0.4s;
}

.title-line:nth-child(3) {
    animation-delay: 0.6s;
}

.highlight {
    background: linear-gradient(45deg, var(--primary-gold), var(--secondary-bronze));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(212, 175, 55, 0.5);
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--light-text);
    margin-bottom: 3rem;
    font-family: var(--garamond);
    font-style: italic;
    line-height: 1.8;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 4rem;
}

.primary-btn, .secondary-btn {
    padding: 1rem 2rem;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
}

.primary-btn {
    background: linear-gradient(45deg, var(--deep-burgundy), var(--rich-brown));
    color: white;
    box-shadow: 0 8px 25px rgba(128, 0, 32, 0.3);
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(128, 0, 32, 0.4);
}

.secondary-btn {
    background: transparent;
    color: var(--deep-burgundy);
    border: 2px solid var(--primary-gold);
}

.secondary-btn:hover {
    background: var(--primary-gold);
    color: white;
    transform: translateY(-2px);
}

.hero-stats {
    display: flex;
    gap: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: var(--vt323);
    font-size: 2.5rem;
    color: var(--deep-burgundy);
    font-weight: bold;
}

.stat-label {
    color: var(--light-text);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Hero Right - Book Stack */
.hero-right {
    position: relative;
    height: 500px;
    animation: slideInRight 1s ease-out;
}

.book-stack {
    position: relative;
    width: 100%;
    height: 100%;
}

.book {
    position: absolute;
    width: 60px;
    height: 300px;
    border-radius: 3px 8px 8px 3px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.book-1 {
    background: linear-gradient(45deg, #8B4513, #A0522D);
    left: 50px;
    top: 100px;
    transform: rotate(-5deg);
    animation-delay: 0.1s;
}

.book-2 {
    background: linear-gradient(45deg, #2F4F4F, #708090);
    left: 120px;
    top: 80px;
    transform: rotate(3deg);
    animation-delay: 0.3s;
}

.book-3 {
    background: linear-gradient(45deg, #800020, #B22222);
    left: 190px;
    top: 90px;
    transform: rotate(-2deg);
    animation-delay: 0.5s;
}

.book-4 {
    background: linear-gradient(45deg, #006400, #228B22);
    left: 260px;
    top: 110px;
    transform: rotate(4deg);
    animation-delay: 0.7s;
}

.book-spine {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.book:hover {
    transform: translateY(-10px) scale(1.05);
    z-index: 10;
}

.floating-pages {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.page {
    position: absolute;
    font-size: 2rem;
    opacity: 0.7;
    animation: float 4s ease-in-out infinite;
}

.page-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.page-2 {
    top: 40%;
    right: 10%;
    animation-delay: 1.3s;
}

.page-3 {
    bottom: 20%;
    left: 30%;
    animation-delay: 2.6s;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: var(--primary-gold);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
    cursor: pointer;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-family: var(--vt323);
    font-size: 3rem;
    color: var(--deep-burgundy);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--light-text);
    font-style: italic;
}

/* About Section */
.about-section {
    padding: 8rem 0;
    background: linear-gradient(135deg, var(--cream) 0%, var(--parchment) 100%);
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.elegant-text {
    font-size: 1.2rem;
    line-height: 2;
    color: var(--dark-text);
    margin-bottom: 2rem;
    font-family: var(--garamond);
    text-align: justify;
}

.quote-carousel {
    position: relative;
    height: 200px;
    background: var(--parchment);
    border-radius: 15px;
    padding: 2rem;
    border-left: 5px solid var(--primary-gold);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.quote {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 2rem;
    opacity: 0;
    transition: opacity 0.5s ease;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.quote.active {
    opacity: 1;
}

.quote p {
    font-size: 1.3rem;
    font-style: italic;
    color: var(--deep-burgundy);
    margin-bottom: 1rem;
    font-family: var(--garamond);
}

.quote cite {
    font-size: 1rem;
    color: var(--light-text);
    text-align: right;
}

/* Features Section */
.features-section {
    padding: 8rem 0;
    background: var(--parchment);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
}

.feature-card {
    background: white;
    padding: 3rem 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--primary-gold);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--primary-gold), var(--secondary-bronze));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    color: white;
    font-size: 2rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    color: var(--deep-burgundy);
    margin-bottom: 1rem;
    font-family: var(--vt323);
}

.feature-card p {
    color: var(--light-text);
    line-height: 1.8;
}

/* CTA Section */
.cta-section {
    padding: 8rem 0;
    background: linear-gradient(135deg, var(--deep-burgundy) 0%, var(--rich-brown) 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-content h2 {
    font-family: var(--vt323);
    font-size: 3rem;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    font-style: italic;
}

.cta-primary {
    background: linear-gradient(45deg, var(--primary-gold), var(--secondary-bronze));
    color: white;
    border: none;
    padding: 1.2rem 3rem;
    border-radius: 35px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.cta-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.3);
}

.cta-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-book {
    position: absolute;
    font-size: 3rem;
    opacity: 0.1;
    animation: floatSlow 8s ease-in-out infinite;
}

.floating-book:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-book:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.floating-book:nth-child(3) {
    bottom: 20%;
    left: 80%;
    animation-delay: 4s;
}

/* Footer */
.landing-footer {
    background: var(--dark-text);
    color: var(--cream);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.footer-logo .logo-text {
    font-family: var(--vt323);
    font-size: 1.5rem;
    color: var(--primary-gold);
}

.footer-links {
    display: flex;
    gap: 2rem;
    justify-content: flex-end;
}

.footer-links a {
    color: var(--cream);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-gold);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--light-text);
    color: var(--light-text);
}

/* Animations */
@keyframes bookFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes slideInLeft {
    0% { transform: translateX(-100px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    0% { transform: translateX(100px); opacity: 0; }
    100% { transform: translateX(0); opacity: 1; }
}

@keyframes fadeInUp {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes float {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(5deg); }
    66% { transform: translateY(-10px) rotate(-3deg); }
}

@keyframes floatSlow {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-30px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-stats {
        justify-content: center;
        gap: 2rem;
    }
    
    .nav-links {
        flex-direction: column;
        gap: 1rem;
    }
    
    .about-content {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .footer-links {
        justify-content: center;
    }
}
