{"name": "giki-virtual-library", "version": "2.0.0", "description": "GIKI University Virtual Library - Complete book sharing platform with authentication and database", "main": "server-database.js", "scripts": {"start": "node server-simple.js", "dev": "nodemon server-simple.js", "database": "node server-database.js", "legacy": "node server.js", "test": "echo \"No tests specified yet\" && exit 0"}, "dependencies": {"express": "^4.21.2", "socket.io": "^4.8.1", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "lowdb": "^7.0.1", "express-session": "^1.18.2", "passport": "^0.7.0", "passport-microsoft": "^1.1.0", "dotenv": "^16.6.1", "express-rate-limit": "^7.5.1", "helmet": "^7.2.0", "morgan": "^1.10.1"}, "devDependencies": {"nodemon": "^3.1.10"}, "keywords": ["library", "books", "sharing", "giki", "university", "o<PERSON>h", "postgresql", "real-time", "notifications"], "author": "GIKI Virtual Library Team", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/giki-library/virtual-library.git"}}